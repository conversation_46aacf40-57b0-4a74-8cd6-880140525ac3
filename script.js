class WhatsAppVisualizer {
    constructor() {
        this.messages = [];
        this.currentRotation = 0;
        this.currentZoom = 1;
        this.audioPlayer = document.getElementById('audioPlayer');
        this.currentAudio = null;
        this.searchResults = [];
        this.currentSearchIndex = -1;
        this.init();
    }

    parseUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        this.conversationPath = urlParams.get('conversation') || '';
        this.contactName = urlParams.get('contact') || 'Contato';

        console.log('Parâmetros parseados:', { conversationPath: this.conversationPath, contactName: this.contactName });

        // Atualiza o título da página e o nome do contato
        document.title = `WhatsApp Visualizer - Conversa com ${this.contactName}`;
        const contactNameElement = document.getElementById('contactName');
        if (contactNameElement) {
            contactNameElement.textContent = this.contactName;
        }

        // Carrega a foto de perfil
        this.loadProfilePicture();
    }

    loadProfilePicture() {
        const profilePic = document.getElementById('profilePic');
        const profileIcon = document.getElementById('profileIcon');

        if (profilePic && this.conversationPath) {
            const profilePicPath = `${this.conversationPath}/profile.jpg`;
            profilePic.src = profilePicPath;
            profilePic.alt = this.contactName;

            // Tenta carregar a imagem
            profilePic.onload = () => {
                profilePic.style.display = 'block';
                profileIcon.style.display = 'none';
            };

            profilePic.onerror = () => {
                profilePic.style.display = 'none';
                profileIcon.style.display = 'flex';
            };
        }
    }

    setupBackButton() {
        const backButton = document.getElementById('backButton');
        if (backButton) {
            backButton.addEventListener('click', () => {
                window.location.href = 'index.html';
            });
        }
    }

    async init() {
        this.parseUrlParams();
        this.setupBackButton();
        await this.loadConversation();
        this.renderMessages();
        this.setupModalHandlers();
        this.setupScrollHandler();
        this.setupSearchHandlers();
    }

    async loadConversation() {
        if (!this.conversationPath) {
            console.error('Caminho da conversa não especificado');
            return;
        }

        try {
            // Carrega dinamicamente o arquivo conversation-data.js da conversa específica
            const script = document.createElement('script');
            script.src = `${this.conversationPath}/conversation-data.js`;

            // Aguarda o carregamento do script
            await new Promise((resolve, reject) => {
                script.onload = () => {
                    console.log('Script carregado:', script.src);
                    // Aguarda um pouco mais para garantir que a função foi definida
                    setTimeout(() => {
                        console.log('Verificando função loadEmbeddedConversationData:', typeof loadEmbeddedConversationData);
                        resolve();
                    }, 200);
                };
                script.onerror = (error) => {
                    console.error('Erro ao carregar script:', error);
                    reject(error);
                };
                document.head.appendChild(script);
            });

            // Usa os dados incorporados do arquivo conversation-data.js carregado
            if (typeof loadEmbeddedConversationData === 'function') {
                console.log('Função encontrada, carregando dados...');
                const conversationText = loadEmbeddedConversationData();
                console.log('Dados carregados, tamanho:', conversationText.length);
                this.parseConversation(conversationText);
            } else {
                console.error('Função loadEmbeddedConversationData não encontrada');
                console.log('Funções disponíveis no window:', Object.keys(window).filter(key => key.includes('load')));
                this.showError('Função de carregamento não encontrada. Verifique o arquivo conversation-data.js.');
            }
        } catch (error) {
            console.error('Erro ao carregar conversa:', error);
            this.showError('Erro ao carregar a conversa. Verifique se o arquivo existe.');
        }
    }

    parseConversation(text) {
        console.log('Texto recebido, tamanho:', text.length);
        console.log('Primeiros 200 caracteres:', text.substring(0, 200));

        const lines = text.split('\n');
        console.log('Número de linhas após split:', lines.length);
        console.log('Primeiras 3 linhas:', lines.slice(0, 3));

        let currentMessage = null;
        const messages = [];

        for (let line of lines) {
            line = line.trim();
            if (!line) continue;

            // Remove <br> tags
            line = line.replace(/<br>/g, '');

            // Check if it's a new message (starts with date pattern)
            const messageMatch = line.match(/^(\d{2}\/\d{2}\/\d{4}) (\d{2}:\d{2}) - (.*)$/);

            if (messageMatch) {
                // Save previous message if exists
                if (currentMessage) {
                    // Update message type after content is complete
                    currentMessage.type = this.getMessageType(currentMessage.content);
                    this.messages.push(currentMessage);
                }

                const [, date, time, rest] = messageMatch;

                // Check if it has a sender (contains colon after name)
                const senderMatch = rest.match(/^([^:]+): (.*)$/);

                if (senderMatch) {
                    // Regular message with sender
                    const [, sender, content] = senderMatch;
                    currentMessage = {
                        date,
                        time,
                        sender: sender.trim(),
                        content: content.trim()
                    };
                } else {
                    // System message without sender
                    currentMessage = {
                        date,
                        time,
                        sender: 'System',
                        content: rest.trim()
                    };
                }
            } else if (currentMessage && line) {
                // Continue previous message
                currentMessage.content += '\n' + line;
            }
        }

        // Add last message
        if (currentMessage) {
            currentMessage.type = this.getMessageType(currentMessage.content);
            this.messages.push(currentMessage);
        }

        console.log(`Parsed ${this.messages.length} messages`);
    }

    getMessageType(content) {
        // Check for system messages first
        if (content.includes('As mensagens e as ligações são protegidas') ||
            content.includes('criptografia de ponta a ponta')) {
            return 'system';
        }

        if (content.includes('(arquivo anexado)')) {
            if (content.includes('.opus') || content.includes('.mp3') || content.includes('AUD-')) {
                return 'audio';
            } else if (content.includes('.jpg') || content.includes('.png') ||
                       content.includes('IMAGEM') || content.includes('IMG-')) {
                return 'image';
            } else if (content.includes('.mp4') || content.includes('.avi') || content.includes('VID-')) {
                return 'video';
            } else if (content.includes('.vcf')) {
                return 'contact';
            } else if (content.includes('.pdf') || content.includes('.doc') || content.includes('.docx') ||
                       content.includes('.txt') || content.includes('.xls') || content.includes('.xlsx') ||
                       content.includes('.ppt') || content.includes('.pptx') || content.includes('.zip') ||
                       content.includes('.rar') || content.includes('.7z')) {
                return 'document';
            }
        } else if (content === '<Mídia oculta>') {
            return 'hidden-media';
        } else if (content === 'Mensagem apagada') {
            return 'deleted';
        } else if (content.includes('http://') || content.includes('https://')) {
            return 'link';
        }
        return 'text';
    }

    renderMessages() {
        const container = document.getElementById('chatMessages');
        let currentDate = '';
        
        this.messages.forEach((message, index) => {
            // Add date separator if date changed
            if (message.date !== currentDate) {
                currentDate = message.date;
                const dateElement = this.createDateSeparator(message.date);
                container.appendChild(dateElement);
            }

            const messageElement = this.createMessageElement(message);
            container.appendChild(messageElement);
        });

        // Scroll to top (início da conversa)
        container.scrollTop = 0;

        // Mostrar FAB inicialmente (já que não estamos no final)
        const scrollToBottomFab = document.getElementById('scrollToBottomFab');
        if (scrollToBottomFab) {
            scrollToBottomFab.classList.remove('hidden');
        }
    }

    createDateSeparator(date) {
        const dateDiv = document.createElement('div');
        dateDiv.className = 'date-separator';
        dateDiv.innerHTML = `<span class="date-badge">${this.formatDate(date)}</span>`;
        return dateDiv;
    }

    createMessageElement(message) {
        // Handle system messages differently
        if (message.type === 'system') {
            const systemDiv = document.createElement('div');
            systemDiv.className = 'system-message';
            systemDiv.innerHTML = this.formatText(message.content);
            return systemDiv;
        }

        const messageDiv = document.createElement('div');
        // Obtém o nome do usuário da configuração
        const userName = (typeof getUserConfig === 'function') ? getUserConfig().name : 'José Gabriel';
        messageDiv.className = `message ${message.sender === userName ? 'sent' : 'received'}`;
        messageDiv.dataset.date = message.date;

        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';

        const content = document.createElement('div');
        content.className = 'message-content';

        // Render content based on message type
        switch (message.type) {
            case 'audio':
                content.appendChild(this.createAudioElement(message));
                break;
            case 'image':
                content.appendChild(this.createImageElement(message));
                break;
            case 'video':
                content.appendChild(this.createVideoElement(message));
                break;
            case 'contact':
                content.appendChild(this.createContactElement(message));
                break;
            case 'document':
                content.appendChild(this.createDocumentElement(message));
                break;
            case 'hidden-media':
                content.innerHTML = '<div class="hidden-media"><i class="fas fa-eye-slash"></i> Mídia oculta</div>';
                break;
            case 'deleted':
                content.innerHTML = '<div class="deleted-message">Mensagem apagada</div>';
                break;
            case 'link':
                content.innerHTML = this.formatLinks(message.content);
                break;
            default:
                content.innerHTML = this.formatText(message.content);
        }

        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = message.time;

        bubble.appendChild(content);
        bubble.appendChild(time);
        messageDiv.appendChild(bubble);

        return messageDiv;
    }

    createAudioElement(message) {
        const audioDiv = document.createElement('div');
        audioDiv.className = 'audio-message';

        const filename = this.extractFilename(message.content);
        const audioPath = `${this.conversationPath}/audios/${filename}`;

        audioDiv.innerHTML = `
            <button class="audio-play-btn" onclick="visualizer.playAudio('${audioPath}', this)">
                <i class="fas fa-play"></i>
            </button>
            <div class="audio-info">
                <div class="audio-progress" onclick="visualizer.seekAudio(event, this)">
                    <div class="audio-progress-bar"></div>
                </div>
                <div class="audio-duration">0:00</div>
            </div>
        `;

        // Carregar duração do áudio
        const audio = new Audio(audioPath);
        const durationElement = audioDiv.querySelector('.audio-duration');

        audio.addEventListener('loadedmetadata', () => {
            const duration = this.formatTime(audio.duration);
            durationElement.textContent = duration;
        });

        // Se não conseguir carregar, manter 0:00
        audio.addEventListener('error', () => {
            durationElement.textContent = '0:00';
        });

        return audioDiv;
    }

    createImageElement(message) {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'media-message';

        let filename;
        if (message.content.includes('IMAGEM')) {
            // Extract from <a href="filename">IMAGEM</a> format
            const match = message.content.match(/href="([^"]+)"/);
            filename = match ? match[1] : '';
        } else {
            filename = this.extractFilename(message.content);
        }

        const imagePath = `${this.conversationPath}/media/${filename}`;
        
        const img = document.createElement('img');
        img.src = imagePath;
        img.alt = 'Imagem';
        img.onclick = () => this.openImageModal(imagePath);
        
        imageDiv.appendChild(img);
        return imageDiv;
    }

    createVideoElement(message) {
        const videoDiv = document.createElement('div');
        videoDiv.className = 'media-message';

        const filename = this.extractFilename(message.content);
        const videoPath = `${this.conversationPath}/media/${filename}`;

        const videoThumb = document.createElement('div');
        videoThumb.className = 'video-thumbnail';
        videoThumb.style.width = '200px';
        videoThumb.style.height = '150px';
        videoThumb.style.background = '#000';
        videoThumb.style.borderRadius = '8px';
        videoThumb.onclick = () => this.openVideoModal(videoPath);

        videoDiv.appendChild(videoThumb);
        return videoDiv;
    }

    createContactElement(message) {
        const contactDiv = document.createElement('div');
        contactDiv.className = 'contact-message';

        const filename = this.extractFilename(message.content);
        const contactName = filename.replace('.vcf', '');

        contactDiv.innerHTML = `
            <div class="contact-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="contact-info-msg">
                <div class="contact-name">${contactName}</div>
                <div class="contact-label">Contato</div>
            </div>
        `;

        return contactDiv;
    }

    createDocumentElement(message) {
        const documentDiv = document.createElement('div');
        documentDiv.className = 'document-message';

        const filename = this.extractFilename(message.content);
        const documentPath = `${this.conversationPath}/documentos/${filename}`;

        // Determinar o tipo de documento e ícone
        const extension = filename.split('.').pop().toLowerCase();
        let icon = 'fas fa-file';
        let color = '#8696a0';

        switch (extension) {
            case 'pdf':
                icon = 'fas fa-file-pdf';
                color = '#dc3545';
                break;
            case 'doc':
            case 'docx':
                icon = 'fas fa-file-word';
                color = '#2b579a';
                break;
            case 'xls':
            case 'xlsx':
                icon = 'fas fa-file-excel';
                color = '#217346';
                break;
            case 'ppt':
            case 'pptx':
                icon = 'fas fa-file-powerpoint';
                color = '#d24726';
                break;
            case 'txt':
                icon = 'fas fa-file-alt';
                color = '#6c757d';
                break;
            case 'zip':
            case 'rar':
            case '7z':
                icon = 'fas fa-file-archive';
                color = '#fd7e14';
                break;
        }

        documentDiv.innerHTML = `
            <div class="document-icon" style="color: ${color};">
                <i class="${icon}"></i>
            </div>
            <div class="document-info">
                <div class="document-name">${filename}</div>
                <div class="document-type">${extension.toUpperCase()}</div>
            </div>
            <button class="document-download" onclick="visualizer.downloadDocument('${documentPath}')">
                <i class="fas fa-download"></i>
            </button>
        `;

        return documentDiv;
    }

    downloadDocument(documentPath) {
        // Verifica se é PDF para abrir em nova aba
        const filename = documentPath.split('/').pop();
        const extension = filename.split('.').pop().toLowerCase();

        if (extension === 'pdf') {
            // Abre PDF em nova aba
            window.open(documentPath, '_blank');
        } else {
            // Para outros tipos, tenta fazer download
            const link = document.createElement('a');
            link.href = documentPath;
            link.download = filename;
            link.click();
        }
    }

    extractFilename(content) {
        const match = content.match(/‎([^(]+)\s*\(arquivo anexado\)/);
        return match ? match[1].trim() : '';
    }

    formatText(text) {
        return text.replace(/\n/g, '<br>');
    }

    formatLinks(text) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        return text.replace(urlRegex, '<a href="$1" target="_blank">$1</a>').replace(/\n/g, '<br>');
    }

    formatDate(dateStr) {
        const [day, month, year] = dateStr.split('/');
        const date = new Date(year, month - 1, day);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Hoje';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Ontem';
        } else {
            return date.toLocaleDateString('pt-BR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    }

    setupModalHandlers() {
        // Image modal
        const imageModal = document.getElementById('imageModal');
        const videoModal = document.getElementById('videoModal');
        
        // Close modals
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.onclick = () => {
                // Parar vídeo se estiver tocando
                const video = videoModal.querySelector('video');
                if (video) {
                    video.pause();
                    video.currentTime = 0;
                }

                imageModal.style.display = 'none';
                videoModal.style.display = 'none';
                this.resetImageTransform();
            };
        });

        // Close modal when clicking outside
        window.onclick = (event) => {
            if (event.target === imageModal) {
                imageModal.style.display = 'none';
                this.resetImageTransform();
            }
            if (event.target === videoModal) {
                // Parar vídeo se estiver tocando
                const video = videoModal.querySelector('video');
                if (video) {
                    video.pause();
                    video.currentTime = 0;
                }
                videoModal.style.display = 'none';
            }
        };
    }

    setupScrollHandler() {
        const chatMessages = document.getElementById('chatMessages');
        const stickyDate = document.getElementById('stickyDate');
        const scrollToBottomFab = document.getElementById('scrollToBottomFab');

        chatMessages.addEventListener('scroll', () => {
            const messages = chatMessages.querySelectorAll('.message');
            let currentVisibleDate = '';

            for (let message of messages) {
                const rect = message.getBoundingClientRect();
                const containerRect = chatMessages.getBoundingClientRect();

                if (rect.top <= containerRect.top + 100) {
                    currentVisibleDate = message.dataset.date;
                }
            }

            if (currentVisibleDate) {
                stickyDate.textContent = this.formatDate(currentVisibleDate);
                stickyDate.style.display = 'block';
            }

            // Show/hide scroll to bottom FAB
            const isAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 100;

            if (isAtBottom) {
                scrollToBottomFab.classList.add('hidden');
            } else {
                scrollToBottomFab.classList.remove('hidden');
            }
        });

        // FAB click handler
        scrollToBottomFab.addEventListener('click', () => {
            chatMessages.scrollTo({
                top: chatMessages.scrollHeight,
                behavior: 'smooth'
            });
        });
    }

    openImageModal(imagePath) {
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');
        modal.style.display = 'block';
        modalImg.src = imagePath;
        this.resetImageTransform();
    }

    openVideoModal(videoPath) {
        const modal = document.getElementById('videoModal');
        const modalVideo = document.getElementById('modalVideo');
        modal.style.display = 'block';
        modalVideo.src = videoPath;

        // Auto-play quando o vídeo carregar
        modalVideo.addEventListener('loadeddata', () => {
            modalVideo.play().catch(error => {
                console.log('Auto-play bloqueado pelo navegador:', error);
            });
        }, { once: true });
    }

    resetImageTransform() {
        this.currentRotation = 0;
        this.currentZoom = 1;
        const img = document.getElementById('modalImage');
        img.style.transform = 'rotate(0deg) scale(1)';
    }

    async playAudio(audioPath, button) {
        const icon = button.querySelector('i');
        const progressBar = button.parentElement.querySelector('.audio-progress-bar');
        const durationElement = button.parentElement.querySelector('.audio-duration');

        // Verifica se já existe um áudio associado a este botão
        let audio = button.audioElement;

        if (!audio) {
            // Cria um novo elemento audio para este botão
            audio = new Audio(audioPath);
            button.audioElement = audio;

            // Configura eventos do áudio
            audio.onended = () => {
                icon.className = 'fas fa-play';
                progressBar.style.width = '0%';
                if (this.currentAudio === audio) {
                    this.currentAudio = null;
                }
            };

            audio.ontimeupdate = () => {
                if (this.currentAudio === audio) {
                    const progress = (audio.currentTime / audio.duration) * 100;
                    progressBar.style.width = progress + '%';
                    durationElement.textContent = this.formatTime(audio.currentTime);
                }
            };
        }

        // Para qualquer outro áudio que esteja tocando
        if (this.currentAudio && this.currentAudio !== audio) {
            this.currentAudio.pause();
            document.querySelectorAll('.audio-play-btn i').forEach(i => {
                if (i.parentElement !== button) {
                    i.className = 'fas fa-play';
                }
            });
        }

        // Controla play/pause do áudio atual
        if (audio.paused) {
            try {
                await audio.play();
                icon.className = 'fas fa-pause';
                this.currentAudio = audio;
            } catch (error) {
                console.error('Erro ao reproduzir áudio:', error);
            }
        } else {
            audio.pause();
            icon.className = 'fas fa-play';
            if (this.currentAudio === audio) {
                this.currentAudio = null;
            }
        }
    }

    seekAudio(event, progressElement) {
        // Encontra o botão de áudio associado
        const audioButton = progressElement.parentElement.parentElement.querySelector('.audio-play-btn');
        const audio = audioButton?.audioElement;

        if (!audio || !audio.duration) {
            return;
        }

        // Calcula a posição clicada como porcentagem
        const rect = progressElement.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;

        // Define o tempo baseado na porcentagem
        const newTime = percentage * audio.duration;
        audio.currentTime = Math.max(0, Math.min(newTime, audio.duration));

        // Atualiza a barra de progresso imediatamente
        const progressBar = progressElement.querySelector('.audio-progress-bar');
        progressBar.style.width = (percentage * 100) + '%';

        // Atualiza o tempo exibido
        const durationElement = progressElement.parentElement.querySelector('.audio-duration');
        durationElement.textContent = this.formatTime(audio.currentTime);
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    setupSearchHandlers() {
        const searchBtn = document.getElementById('searchBtn');
        const searchPanel = document.getElementById('searchPanel');
        const searchInput = document.getElementById('searchInput');
        const searchClose = document.getElementById('searchClose');
        const searchPrev = document.getElementById('searchPrev');
        const searchNext = document.getElementById('searchNext');
        const searchCounter = document.getElementById('searchCounter');

        // Toggle search panel
        searchBtn.addEventListener('click', () => {
            searchPanel.classList.toggle('active');
            if (searchPanel.classList.contains('active')) {
                searchInput.focus();
            } else {
                this.clearSearch();
            }
        });

        // Close search panel
        searchClose.addEventListener('click', () => {
            searchPanel.classList.remove('active');
            this.clearSearch();
        });

        // Search input
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length > 0) {
                this.performSearch(query);
            } else {
                this.clearSearch();
            }
        });

        // Navigation buttons
        searchPrev.addEventListener('click', () => {
            this.navigateSearch(-1);
        });

        searchNext.addEventListener('click', () => {
            this.navigateSearch(1);
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && searchPanel.classList.contains('active')) {
                searchPanel.classList.remove('active');
                this.clearSearch();
            }
        });
    }

    performSearch(query) {
        this.clearSearch();
        this.searchResults = [];

        const chatMessages = document.getElementById('chatMessages');
        const messages = chatMessages.querySelectorAll('.message-content');

        messages.forEach((messageContent, index) => {
            const text = messageContent.textContent.toLowerCase();
            const searchQuery = query.toLowerCase();

            if (text.includes(searchQuery)) {
                // Highlight matches in this message
                this.highlightMatches(messageContent, query);
                this.searchResults.push({
                    element: messageContent.closest('.message'),
                    messageContent: messageContent
                });
            }
        });

        this.updateSearchCounter();
        if (this.searchResults.length > 0) {
            this.currentSearchIndex = 0;
            this.highlightCurrentResult();
            this.scrollToCurrentResult();
        }
    }

    highlightMatches(element, query) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');

            if (regex.test(text)) {
                const highlightedHTML = text.replace(regex, '<span class="search-highlight">$1</span>');
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedHTML;
                textNode.parentNode.replaceChild(wrapper, textNode);
            }
        });
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    clearSearch() {
        // Remove all highlights
        document.querySelectorAll('.search-highlight').forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });

        this.searchResults = [];
        this.currentSearchIndex = -1;
        this.updateSearchCounter();
    }

    navigateSearch(direction) {
        if (this.searchResults.length === 0) return;

        this.currentSearchIndex += direction;

        if (this.currentSearchIndex >= this.searchResults.length) {
            this.currentSearchIndex = 0;
        } else if (this.currentSearchIndex < 0) {
            this.currentSearchIndex = this.searchResults.length - 1;
        }

        this.highlightCurrentResult();
        this.scrollToCurrentResult();
        this.updateSearchCounter();
    }

    highlightCurrentResult() {
        // Remove current highlight
        document.querySelectorAll('.search-highlight.current').forEach(el => {
            el.classList.remove('current');
        });

        if (this.searchResults.length > 0 && this.currentSearchIndex >= 0) {
            const currentResult = this.searchResults[this.currentSearchIndex];
            const highlights = currentResult.messageContent.querySelectorAll('.search-highlight');
            if (highlights.length > 0) {
                highlights[0].classList.add('current');
            }
        }
    }

    scrollToCurrentResult() {
        if (this.searchResults.length > 0 && this.currentSearchIndex >= 0) {
            const currentResult = this.searchResults[this.currentSearchIndex];
            currentResult.element.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }

    updateSearchCounter() {
        const searchCounter = document.getElementById('searchCounter');
        const searchPrev = document.getElementById('searchPrev');
        const searchNext = document.getElementById('searchNext');

        if (this.searchResults.length === 0) {
            searchCounter.textContent = '0 de 0';
            searchPrev.disabled = true;
            searchNext.disabled = true;
        } else {
            searchCounter.textContent = `${this.currentSearchIndex + 1} de ${this.searchResults.length}`;
            searchPrev.disabled = false;
            searchNext.disabled = false;
        }
    }
}

// Global functions for modal controls
function rotateImage() {
    visualizer.currentRotation += 90;
    const img = document.getElementById('modalImage');
    img.style.transform = `rotate(${visualizer.currentRotation}deg) scale(${visualizer.currentZoom})`;
}

function zoomImage(factor) {
    visualizer.currentZoom *= factor;
    const img = document.getElementById('modalImage');
    img.style.transform = `rotate(${visualizer.currentRotation}deg) scale(${visualizer.currentZoom})`;
}

// Initialize the visualizer - Updated
const visualizer = new WhatsAppVisualizer();
