#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
import glob

def read_conversation_file():
    """Lê o arquivo de conversa e retorna o conteúdo"""
    try:
        # Encontrar todos os arquivos .txt no diretório atual
        arquivos_txt = glob.glob('*.txt')

        # Verificar se há exatamente um arquivo .txt
        if len(arquivos_txt) == 1:
            nome_arquivo = arquivos_txt[0]
            with open(nome_arquivo, 'r', encoding='utf-8') as file:
                return file.read()
        else:
            print("Não foi encontrado exatamente um arquivo .txt no diretório.")
    except Exception as e:
        print(f"Erro ao ler arquivo: {e}")
        return ""

def escape_javascript_string(text):
    """Escapa caracteres especiais para uso em string JavaScript"""
    # Escapa caracteres especiais mas mantém quebras de linha reais
    text = text.replace('\\', '\\\\')
    text = text.replace('`', '\\`')
    text = text.replace('${', '\\${')
    return text

def generate_javascript_data():
    """Gera o código JavaScript com os dados da conversa"""
    conversation_text = read_conversation_file()

    if not conversation_text:
        print("Não foi possível ler o arquivo de conversa")
        return

    # Remove tags <br> do texto original (incluindo variações)
    import re
    conversation_text = re.sub(r'<br\s*/?>', '', conversation_text)

    # Escapa o texto para JavaScript
    escaped_text = escape_javascript_string(conversation_text)
    
    # Gera o código JavaScript
    js_code = f'''// Dados da conversa incorporados
const CONVERSATION_DATA = `{escaped_text}`;

// Função para carregar os dados incorporados
function loadEmbeddedConversationData() {{
    return CONVERSATION_DATA;
}}
'''
    
    # Salva o arquivo
    with open('conversation-data.js', 'w', encoding='utf-8') as file:
        file.write(js_code)
    
    print("Arquivo conversation-data.js gerado com sucesso!")
    print(f"Tamanho do arquivo original: {len(conversation_text)} caracteres")

if __name__ == "__main__":
    generate_javascript_data()
