<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Visualizer</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="whatsapp-container">
        <!-- Header -->
        <div class="chat-header">
            <div class="contact-info">
                <button class="back-button" id="backButton" title="Voltar">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="avatar" id="contactAvatar">
                    <img id="profilePic" class="profile-pic" alt="Profile" style="display: none;"
                         onerror="this.style.display='none'; document.getElementById('profileIcon').style.display='flex';">
                    <div id="profileIcon" class="profile-icon">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="contact-details">
                    <h3 id="contactName">Carregando...</h3>
                    <span class="status">online</span>
                </div>
            </div>
            <div class="header-actions">
                <i class="fas fa-search" id="searchBtn" title="Pesquisar"></i>
                <i class="fas fa-video"></i>
                <i class="fas fa-phone"></i>
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>

        <!-- Search Panel -->
        <div class="search-panel" id="searchPanel">
            <div class="search-header">
                <input type="text" id="searchInput" placeholder="Pesquisar mensagens...">
                <button class="search-close" id="searchClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results">
                <div class="search-navigation">
                    <button class="search-nav-btn" id="searchPrev" disabled>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <span class="search-counter" id="searchCounter">0 de 0</span>
                    <button class="search-nav-btn" id="searchNext" disabled>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages Container -->
        <div class="chat-messages" id="chatMessages">
            <!-- Sticky Date Header -->
            <div class="sticky-date" id="stickyDate"></div>

            <!-- Messages will be loaded here -->
        </div>

        <!-- Scroll to Bottom FAB -->
        <div class="scroll-to-bottom-fab" id="scrollToBottomFab">
            <i class="fas fa-chevron-down"></i>
        </div>

        <!-- Input Area -->
        <div class="chat-input">
            <div class="input-container">
                <i class="fas fa-smile emoji-btn"></i>
                <i class="fas fa-paperclip attach-btn"></i>
                <input type="text" placeholder="Digite uma mensagem" disabled>
                <i class="fas fa-microphone mic-btn"></i>
            </div>
        </div>
    </div>

    <!-- Image Viewer Modal -->
    <div class="image-modal" id="imageModal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <img id="modalImage" src="" alt="">
            <div class="image-controls">
                <button class="rotate-btn" onclick="rotateImage()">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="zoom-in-btn" onclick="zoomImage(1.2)">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="zoom-out-btn" onclick="zoomImage(0.8)">
                    <i class="fas fa-search-minus"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Video Player Modal -->
    <div class="video-modal" id="videoModal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <video id="modalVideo" controls>
                <source src="" type="video/mp4">
                Seu navegador não suporta o elemento de vídeo.
            </video>
        </div>
    </div>

    <!-- Audio Player -->
    <audio id="audioPlayer" preload="none"></audio>

    <script id="conversationDataScript"></script>
    <script src="conversations.js"></script>
    <script src="script.js"></script>
</body>
</html>
